import numpy as np
import FreeCAD
import Points
import Part
import Mesh
import Draft

def create_sample_point_cloud():
    """
    创建一个示例点云数据用于测试
    """
    # 生成一个球体表面的点云作为示例
    u = np.linspace(0, 2 * np.pi, 50)
    v = np.linspace(0, np.pi, 25)
    x = 10 * np.outer(np.cos(u), np.sin(v)).flatten()
    y = 10 * np.outer(np.sin(u), np.sin(v)).flatten()
    z = 10 * np.outer(np.ones(np.size(u)), np.cos(v)).flatten()
    
    points = []
    for i in range(len(x)):
        points.append(FreeCAD.Vector(x[i], y[i], z[i]))
    
    return points

def point_cloud_to_points_object(points):
    """
    将点列表转换为FreeCAD Points对象
    
    Args:
        points: 点坐标列表，每个元素为FreeCAD.Vector
    
    Returns:
        Points::Feature对象
    """
    # 创建新的文档
    if FreeCAD.ActiveDocument is None:
        doc = FreeCAD.newDocument("PointCloudModel")
    else:
        doc = FreeCAD.ActiveDocument
    
    # 创建点云对象
    point_cloud = doc.addObject("Points::Feature", "PointCloud")
    # 修复：使用Points.Points()构造函数创建Points对象
    point_cloud.Points = Points.Points(points)
    
    return doc, point_cloud

def point_cloud_to_mesh(points, method="ball_pivoting"):
    """
    将点云转换为网格模型
    
    Args:
        points: 点坐标列表，每个元素为FreeCAD.Vector
        method: 转换方法 ("ball_pivoting" 或 "delaunay")
    
    Returns:
        Mesh::Feature对象
    """
    if FreeCAD.ActiveDocument is None:
        doc = FreeCAD.newDocument("PointCloudModel")
    else:
        doc = FreeCAD.ActiveDocument
    
    # 创建点对象以便处理
    point_obj = doc.addObject("Points::Feature", "TempPoints")
    # 修复：使用Points.Points()构造函数创建Points对象
    point_obj.Points = Points.Points(points)
    
    if method == "ball_pivoting":
        # 使用滚球算法创建网格
        # 首先创建凸包
        try:
            # 尝试创建Delaunay网格
            mesh = doc.addObject("Mesh::Feature", "Mesh")
            mesh.Mesh = Mesh.Mesh(points)
            # 删除临时点对象
            doc.removeObject(point_obj.Name)
            return doc, mesh
        except:
            # 如果失败，创建简单的点表示
            point_cloud = doc.addObject("Points::Feature", "PointCloud")
            point_cloud.Points = Points.Points(points)
            # 删除临时点对象
            doc.removeObject(point_obj.Name)
            return doc, point_cloud
    else:
        # 默认方法：直接从点创建网格
        mesh = doc.addObject("Mesh::Feature", "Mesh")
        mesh.Mesh = Mesh.Mesh(points)
        # 删除临时点对象
        doc.removeObject(point_obj.Name)
        return doc, mesh

def point_cloud_to_surface(points):
    """
    将点云转换为表面
    
    Args:
        points: 点坐标列表，每个元素为FreeCAD.Vector
    
    Returns:
        Part::Feature对象
    """
    if FreeCAD.ActiveDocument is None:
        doc = FreeCAD.newDocument("PointCloudModel")
    else:
        doc = FreeCAD.ActiveDocument
    
    try:
        # 尝试创建插值表面
        # 使用拟合方法创建表面
        points_array = [(p.x, p.y, p.z) for p in points]
        
        # 创建BSpline表面
        surface = doc.addObject("Part::Spline", "Surface")
        # 简化处理：创建凸包
        mesh = Mesh.Mesh(points)
        shape = Part.Shape()
        shape.makeShapeFromMesh(mesh.Topology, 0.1)
        solid = Part.makeSolid(shape)
        solid_obj = doc.addObject("Part::Feature", "Solid")
        solid_obj.Shape = solid
        
        return doc, solid_obj
    except:
        # 如果失败，回退到点云显示
        point_cloud = doc.addObject("Points::Feature", "PointCloud")
        point_cloud.Points = Points.Points(points)
        return doc, point_cloud

def load_point_cloud_from_file(filepath):
    """
    从文件加载点云数据
    
    Args:
        filepath: 点云文件路径 (.asc, .pcd, .txt等格式)
    
    Returns:
        点坐标列表
    """
    points = []
    
    try:
        # 尝试读取不同格式的点云文件
        with open(filepath, 'r') as f:
            for line in f:
                coords = line.strip().split()
                if len(coords) >= 3:
                    x, y, z = float(coords[0]), float(coords[1]), float(coords[2])
                    points.append(FreeCAD.Vector(x, y, z))
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None
    
    return points

def run_example():
    """
    运行示例：演示点云到3D模型的转换
    """
    # 创建示例点云
    print("创建示例点云...")
    points = create_sample_point_cloud()
    print(f"生成了 {len(points)} 个点")
    
    # 方法1: 显示为点云
    print("方法1: 创建点云对象...")
    doc, point_obj = point_cloud_to_points_object(points)
    
    # 方法2: 转换为网格
    print("方法2: 转换为网格...")
    doc, mesh_obj = point_cloud_to_mesh(points)
    
    # 方法3: 转换为表面
    print("方法3: 转换为表面...")
    doc, surface_obj = point_cloud_to_surface(points)
    
    # 更新视图
    doc.recompute()
    if hasattr(FreeCAD, "Gui"):
        FreeCAD.Gui.SendMsgToActiveView("ViewFit")
        FreeCAD.Gui.activeDocument().activeView().viewAxometric()
    
    print("转换完成！")
    print("文档中包含了:")
    print("- PointCloud: 点云对象")
    print("- Mesh: 网格对象")
    print("- Solid: 实体对象")
    
    return doc

def convert_file_point_cloud(filepath):
    """
    从文件加载点云并转换为3D模型
    
    Args:
        filepath: 点云文件路径
    """
    print(f"正在加载点云文件: {filepath}")
    points = load_point_cloud_from_file(filepath)
    
    if points is None or len(points) == 0:
        print("加载点云失败或点云为空")
        return None
    
    print(f"成功加载 {len(points)} 个点")
    
    # 转换为不同形式的3D模型
    doc, point_obj = point_cloud_to_points_object(points)
    doc, mesh_obj = point_cloud_to_mesh(points)
    doc, surface_obj = point_cloud_to_surface(points)
    
    doc.recompute()
    if hasattr(FreeCAD, "Gui"):
        FreeCAD.Gui.SendMsgToActiveView("ViewFit")
        FreeCAD.Gui.activeDocument().activeView().viewAxometric()
    
    print("点云转换完成！")
    return doc

# 当脚本直接运行时执行示例
if __name__ == "__main__":
    run_example()