import numpy as np
import FreeCAD
import Points
import Part
import Mesh

def create_sample_point_cloud():
    """
    创建一个示例点云数据用于测试
    """
    # 生成一个球体表面的点云作为示例
    u = np.linspace(0, 2 * np.pi, 30)
    v = np.linspace(0, np.pi, 15)
    x = 10 * np.outer(np.cos(u), np.sin(v)).flatten()
    y = 10 * np.outer(np.sin(u), np.sin(v)).flatten()
    z = 10 * np.outer(np.ones(np.size(u)), np.cos(v)).flatten()
    
    points = []
    for i in range(len(x)):
        points.append(FreeCAD.Vector(x[i], y[i], z[i]))
    
    return points

def run_example():
    """
    运行示例：演示点云到3D模型的转换
    """
    # 创建新的文档
    if FreeCAD.ActiveDocument is None:
        doc = FreeCAD.newDocument("PointCloudModel")
    else:
        doc = FreeCAD.ActiveDocument
    
    # 创建示例点云
    print("创建示例点云...")
    points_list = create_sample_point_cloud()
    print(f"生成了 {len(points_list)} 个点")
    
    # 方法1: 显示为点云
    print("方法1: 创建点云对象...")
    point_cloud = doc.addObject("Points::Feature", "PointCloud")
    point_cloud.Points = Points.Points(points_list)
    
    # 方法2: 转换为网格
    print("方法2: 转换为网格...")
    try:
        mesh = doc.addObject("Mesh::Feature", "Mesh")
        mesh.Mesh = Mesh.Mesh(points_list)
    except Exception as e:
        print(f"网格创建失败: {e}")
    
    # 更新视图
    doc.recompute()
    if hasattr(FreeCAD, "Gui") and FreeCAD.Gui.isActive():
        FreeCAD.Gui.SendMsgToActiveView("ViewFit")
        FreeCAD.Gui.activeDocument().activeView().viewAxometric()
    
    print("转换完成！")

# 直接运行示例
run_example()