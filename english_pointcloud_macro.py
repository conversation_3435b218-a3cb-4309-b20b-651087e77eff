import numpy as np
import FreeCAD
import Points
import Mesh

u = np.linspace(0, 2 * np.pi, 20)
v = np.linspace(0, np.pi, 10)
x = 10 * np.outer(np.cos(u), np.sin(v)).flatten()
y = 10 * np.outer(np.sin(u), np.sin(v)).flatten()
z = 10 * np.outer(np.ones(np.size(u)), np.cos(v)).flatten()

points_list = []
i = 0
while i < len(x):
    points_list.append(FreeCAD.Vector(x[i], y[i], z[i]))
    i = i + 1

if FreeCAD.ActiveDocument is None:
    doc = FreeCAD.newDocument("PointCloud")
else:
    doc = FreeCAD.ActiveDocument

point_cloud = doc.addObject("Points::Feature", "MyPointCloud")
point_cloud.Points = Points.Points(points_list)

try:
    mesh_obj = doc.addObject("Mesh::Feature", "MyMesh")
    mesh_obj.Mesh = Mesh.Mesh(points_list)
except:
    print("Mesh creation failed")

doc.recompute()

try:
    FreeCAD.Gui.SendMsgToActiveView("ViewFit")
    FreeCAD.Gui.activeDocument().activeView().viewAxometric()
except:
    pass

print("Point cloud processing completed. Created " + str(len(points_list)) + " points")