# FreeCAD点云处理宏 - 完全平铺版本

# 导入必要模块
import numpy as np
import FreeCAD
import Points
import Mesh

# 生成球体表面点云数据
u = np.linspace(0, 2 * np.pi, 20)
v = np.linspace(0, np.pi, 10)
x = 10 * np.outer(np.cos(u), np.sin(v)).flatten()
y = 10 * np.outer(np.sin(u), np.sin(v)).flatten()
z = 10 * np.outer(np.ones(np.size(u)), np.cos(v)).flatten()

# 创建FreeCAD向量点列表
points_list = []
i = 0
while i < len(x):
    points_list.append(FreeCAD.Vector(x[i], y[i], z[i]))
    i = i + 1

# 创建新文档
if FreeCAD.ActiveDocument is None:
    doc = FreeCAD.newDocument("PointCloud")
else:
    doc = FreeCAD.ActiveDocument

# 创建点云对象
point_cloud = doc.addObject("Points::Feature", "MyPointCloud")
point_cloud.Points = Points.Points(points_list)

# 尝试创建网格
try:
    mesh_obj = doc.addObject("Mesh::Feature", "MyMesh")
    mesh_obj.Mesh = Mesh.Mesh(points_list)
except:
    print("网格创建失败")

# 刷新文档
doc.recompute()

# 调整视图
try:
    FreeCAD.Gui.SendMsgToActiveView("ViewFit")
    FreeCAD.Gui.activeDocument().activeView().viewAxometric()
except:
    pass

# 输出完成信息
print("点云处理完成，共创建了 " + str(len(points_list)) + " 个点")