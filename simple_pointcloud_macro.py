import numpy as np
import FreeCAD
import Points
import Mesh

# 生成一个球体表面的点云作为示例
u = np.linspace(0, 2 * np.pi, 30)
v = np.linspace(0, np.pi, 15)
x = 10 * np.outer(np.cos(u), np.sin(v)).flatten()
y = 10 * np.outer(np.sin(u), np.sin(v)).flatten()
z = 10 * np.outer(np.ones(np.size(u)), np.cos(v)).flatten()

# 创建点列表
points_list = []
for i in range(len(x)):
    points_list.append(FreeCAD.Vector(x[i], y[i], z[i]))

# 创建新的文档
if FreeCAD.ActiveDocument is None:
    doc = FreeCAD.newDocument("PointCloudModel")
else:
    doc = FreeCAD.ActiveDocument

# 创建点云对象
point_cloud = doc.addObject("Points::Feature", "PointCloud")
point_cloud.Points = Points.Points(points_list)

# 尝试创建网格对象
try:
    mesh = doc.addObject("Mesh::Feature", "Mesh")
    mesh.Mesh = Mesh.Mesh(points_list)
except Exception as e:
    print(f"网格创建失败: {e}")

# 更新视图
doc.recompute()
if hasattr(FreeCAD, "Gui") and FreeCAD.Gui.isActive():
    FreeCAD.Gui.SendMsgToActiveView("ViewFit")
    FreeCAD.Gui.activeDocument().activeView().viewAxometric()

print("点云转换完成！创建了 {} 个点".format(len(points_list)))